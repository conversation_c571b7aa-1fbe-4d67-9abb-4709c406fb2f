{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"dx-connect-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "css"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/dx-connect-ui", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "css", "assets": [{"glob": "**/*", "input": "public"}, "src/logo.ico", "src/assets", {"glob": "**/*", "input": "node_modules/monaco-editor", "output": "assets/monaco/"}], "styles": ["node_modules/@dx-ui/ui/styles/tokens/purple.css", "src/styles/styles.css", "node_modules/@dx-ui/ui/styles/styles.css", "node_modules/highlight.js/styles/github.css", "node_modules/monaco-editor/min/vs/editor/editor.main.css", "node_modules/quill/dist/quill.snow.css"], "stylePreprocessorOptions": {"includePaths": ["node_modules"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "3MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "20kB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "labs": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "3MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "20kB"}], "outputHashing": "all", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.labs.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "dx-connect-ui:build:production"}, "development": {"buildTarget": "dx-connect-ui:build:development", "proxyConfig": "proxy.conf.json", "hmr": true}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing", "@angular/localize/init"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "css", "assets": [{"glob": "**/*", "input": "public"}, "src/logo.ico", "src/assets"], "styles": ["node_modules/@dx-ui/ui/styles/tokens/purple.css", "src/styles/styles.css", "node_modules/@dx-ui/ui/styles/styles.css", "node_modules/highlight.js/styles/github.css", "node_modules/monaco-editor/min/vs/editor/editor.main.css", "node_modules/quill/dist/quill.snow.css"], "stylePreprocessorOptions": {"includePaths": ["node_modules"]}, "scripts": []}}}, "i18n": {"sourceLocale": "en-US", "locales": {"en": "src/assets/i18n/en.json", "vi": "src/assets/i18n/vi.json"}}}}, "cli": {"analytics": false}, "schematics": {"@schematics/angular:component": {"type": "component"}, "@schematics/angular:directive": {"type": "directive"}, "@schematics/angular:service": {"type": "service"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}}}