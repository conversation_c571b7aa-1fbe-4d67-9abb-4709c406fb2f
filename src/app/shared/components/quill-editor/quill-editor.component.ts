import { CommonModule } from '@angular/common';
import { Component, forwardRef, input, OnInit, OnDestroy, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import {
  ControlValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import Quill from 'quill';

@Component({
  selector: 'app-quill-editor',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
  ],
  templateUrl: './quill-editor.component.html',
  styleUrl: './quill-editor.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => QuillEditorComponent),
      multi: true,
    },
  ],
})
export class QuillEditorComponent implements ControlValueAccessor, OnInit, AfterViewInit, OnDestroy {
  @ViewChild('editorContainer', { static: true }) editorContainer!: ElementRef;

  // Input properties
  placeholder = input<string>('Enter text...');
  disabled = input<boolean>(false);
  readonly = input<boolean>(false);
  theme = input<'snow' | 'bubble'>('snow');
  height = input<string>('200px');

  private quill: Quill | null = null;
  private value: string = '';
  private onChange: any = () => {};
  private onTouched: any = () => {};
  private isDisabled: boolean = false;

  ngOnInit() {
    this.isDisabled = this.disabled();
  }

  ngAfterViewInit() {
    this.initializeQuill();
  }

  ngOnDestroy() {
    if (this.quill) {
      this.quill = null;
    }
  }

  private initializeQuill() {
    if (!this.editorContainer) return;

    const toolbarOptions = [
      ['bold', 'italic', 'underline', 'strike'],
      ['blockquote', 'code-block'],
      [{ 'header': 1 }, { 'header': 2 }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'script': 'sub'}, { 'script': 'super' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'direction': 'rtl' }],
      [{ 'size': ['small', false, 'large', 'huge'] }],
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'font': [] }],
      [{ 'align': [] }],
      ['clean'],
      ['link']
    ];

    this.quill = new Quill(this.editorContainer.nativeElement, {
      theme: this.theme(),
      placeholder: this.placeholder(),
      readOnly: this.readonly() || this.isDisabled,
      modules: {
        toolbar: toolbarOptions
      }
    });

    // Set initial content
    if (this.value) {
      this.quill.root.innerHTML = this.value;
    }

    // Listen for text changes
    this.quill.on('text-change', () => {
      if (this.quill) {
        const html = this.quill.root.innerHTML;
        this.value = html === '<p><br></p>' ? '' : html;
        this.onChange(this.value);
      }
    });

    // Listen for selection changes (for onTouched)
    this.quill.on('selection-change', (range) => {
      if (range === null) {
        this.onTouched();
      }
    });

    // Set editor height
    const editorElement = this.editorContainer.nativeElement.querySelector('.ql-editor');
    if (editorElement) {
      editorElement.style.minHeight = this.height();
    }
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    this.value = value || '';
    if (this.quill) {
      this.quill.root.innerHTML = this.value;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled = isDisabled;
    if (this.quill) {
      this.quill.enable(!isDisabled);
    }
  }
}
