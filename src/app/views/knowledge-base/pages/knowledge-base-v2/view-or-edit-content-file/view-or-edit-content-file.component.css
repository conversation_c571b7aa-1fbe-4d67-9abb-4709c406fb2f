::ng-deep .ql-container {
  font-family: inherit;
  font-size: 14px;
  height: calc(100% - 42px);
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

::ng-deep .ql-toolbar {
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

::ng-deep .ql-editor {
  min-height: 350px;
}

::ng-deep .ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
}

::ng-deep .ql-snow .ql-tooltip {
  z-index: 10000;
}

::ng-deep .dark .ql-toolbar {
  background-color: var(--dark-base-400);
  border-color: var(--dark-primary-border);
}

::ng-deep .dark .ql-container {
  background-color: var(--dark-base-300);
  color: var(--dark-base-content);
  border-color: var(--dark-primary-border);
}

::ng-deep .dark .ql-editor.ql-blank::before {
  color: var(--dark-neutral-content);
}

::ng-deep .dark .ql-toolbar button:hover,
::ng-deep .dark .ql-toolbar button:focus,
::ng-deep .dark .ql-toolbar button.ql-active,
::ng-deep .dark .ql-toolbar .ql-picker-label:hover,
::ng-deep .dark .ql-toolbar .ql-picker-label.ql-active,
::ng-deep .dark .ql-toolbar .ql-picker-item:hover,
::ng-deep .dark .ql-toolbar .ql-picker-item.ql-selected {
  color: var(--dark-primary);
}

::ng-deep .dark .ql-toolbar button:hover .ql-fill,
::ng-deep .dark .ql-toolbar button:focus .ql-fill,
::ng-deep .dark .ql-toolbar button.ql-active .ql-fill,
::ng-deep .dark .ql-toolbar .ql-picker-label:hover .ql-fill,
::ng-deep .dark .ql-toolbar .ql-picker-label.ql-active .ql-fill,
::ng-deep .dark .ql-toolbar .ql-picker-item:hover .ql-fill,
::ng-deep .dark .ql-toolbar .ql-picker-item.ql-selected .ql-fill,
::ng-deep .dark .ql-toolbar button:hover .ql-stroke,
::ng-deep .dark .ql-toolbar button:focus .ql-stroke,
::ng-deep .dark .ql-toolbar button.ql-active .ql-stroke,
::ng-deep .dark .ql-toolbar .ql-picker-label:hover .ql-stroke,
::ng-deep .dark .ql-toolbar .ql-picker-label.ql-active .ql-stroke,
::ng-deep .dark .ql-toolbar .ql-picker-item:hover .ql-stroke,
::ng-deep .dark .ql-toolbar .ql-picker-item.ql-selected .ql-stroke {
  fill: var(--dark-primary);
  stroke: var(--dark-primary);
}

::ng-deep .dark .ql-toolbar .ql-stroke {
  fill: none;
  stroke: var(--dark-neutral-content);
}

::ng-deep .dark .ql-toolbar .ql-fill {
  fill: var(--dark-neutral-content);
  stroke: none;
}

::ng-deep .dark .ql-toolbar .ql-picker {
  color: var(--dark-neutral-content);
}

::ng-deep .dark .ql-snow.ql-toolbar button:hover,
::ng-deep .dark .ql-snow .ql-toolbar button:hover,
::ng-deep .dark .ql-snow.ql-toolbar button:focus,
::ng-deep .dark .ql-snow .ql-toolbar button:focus,
::ng-deep .dark .ql-snow.ql-toolbar button.ql-active,
::ng-deep .dark .ql-snow .ql-toolbar button.ql-active {
  background-color: rgba(var(--dark-primary-rgb), 0.1);
}